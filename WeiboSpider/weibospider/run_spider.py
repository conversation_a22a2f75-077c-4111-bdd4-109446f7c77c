#!/usr/bin/env python
# encoding: utf-8
"""
Author: nghuyong
Mail: <EMAIL>
Created Time: 2019-12-07 21:27
"""
import os
import sys
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings
from spiders.tweet_by_user_id import TweetSpiderByUserID
from spiders.tweet_by_keyword import TweetSpiderByKeyword
from spiders.tweet_by_tweet_id import TweetSpiderByTweetID
from spiders.comment import CommentSpider
from spiders.follower import FollowerSpider
from spiders.user import UserSpider
from spiders.fan import FanSpider
from spiders.repost import RepostSpider

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("Usage: python run_spider.py <mode> [tweet_id]")
        sys.exit(1)

    mode = sys.argv[1]
    tweet_id = sys.argv[2] if len(sys.argv) > 2 else None
    
    os.environ['SCRAPY_SETTINGS_MODULE'] = 'settings'
    settings = get_project_settings()
    process = CrawlerProcess(settings)
    mode_to_spider = {
        'comment': CommentSpider,
        'fan': <PERSON><PERSON><PERSON><PERSON>,
        'follow': FollowerSpider,
        'user': UserSpider,
        'repost': RepostSpider,
        'tweet_by_tweet_id': TweetSpiderByTweetID,
        'tweet_by_user_id': TweetSpiderByUserID,
        'tweet_by_keyword': TweetSpiderByKeyword,
    }
    spider_cls = mode_to_spider[mode]
    kwargs = {'tweet_id': tweet_id} if tweet_id else {}
    process.crawl(spider_cls, **kwargs)
    # the script will block here until the crawling is finished
    process.start()
